{"name": "bandicut-clone", "version": "1.0.0", "description": "تطبيق ويب لقطع وتحرير الفيديو مشابه لبرنامج Bandicut - A web-based video cutting application similar to Bandicut", "main": "main.js", "homepage": "https://github.com/your-username/bandicut-clone#readme", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "dev": "webpack serve --mode development --open", "serve": "webpack serve --mode development --host 0.0.0.0", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && electron-builder", "preelectron-pack": "npm run build", "dist": "npm run build && electron-builder --publish=never", "dist-win": "npm run build && electron-builder --win", "dist-mac": "npm run build && electron-builder --mac", "dist-linux": "npm run build && electron-builder --linux"}, "dependencies": {"lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "concurrently": "^9.2.0", "css-loader": "^6.8.0", "electron": "^36.5.0", "electron-builder": "^26.0.12", "html-webpack-plugin": "^5.5.0", "style-loader": "^3.3.0", "wait-on": "^8.0.3", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "keywords": ["video", "cutting", "editing", "bandicut", "react", "video-editor", "web-app", "arabic", "تقطيع-فيديو"], "author": "Bandicut Clone Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/bandicut-clone.git"}, "bugs": {"url": "https://github.com/your-username/bandicut-clone/issues"}, "build": {"appId": "com.bandicutclone.app", "productName": "Bandicut Clone", "directories": {"output": "release"}, "files": ["dist/**/*", "main.js", "package.json"], "mac": {"category": "public.app-category.video", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}