import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Upload,
  Play,
  Pause,
  Square,
  Scissors,
  Download,
  SkipBack,
  SkipForward,
  Volume2,
  FileVideo,
  Loader
} from 'lucide-react';

function App() {
  const [videoFile, setVideoFile] = useState(null);
  const [videoUrl, setVideoUrl] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [startTime, setStartTime] = useState(0);
  const [endTime, setEndTime] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [volume, setVolume] = useState(1);

  const videoRef = useRef(null);
  const fileInputRef = useRef(null);
  const timelineRef = useRef(null);

  // Handle file upload
  const handleFileUpload = useCallback((file) => {
    if (file && file.type.startsWith('video/')) {
      setVideoFile(file);
      const url = URL.createObjectURL(file);
      setVideoUrl(url);
      setCurrentTime(0);
      setStartTime(0);
      setIsPlaying(false);
      setIsProcessing(false);
    } else {
      alert('يرجى اختيار ملف فيديو صالح');
    }
  }, []);

  // Handle drag and drop
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, [handleFileUpload]);

  // Handle file input change
  const handleFileInputChange = useCallback((e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileUpload(file);
    }
  }, [handleFileUpload]);

  // Video controls
  const togglePlay = useCallback(() => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  }, [isPlaying]);

  const handleVideoLoadedMetadata = useCallback(() => {
    if (videoRef.current) {
      const videoDuration = videoRef.current.duration;
      setDuration(videoDuration);
      setEndTime(videoDuration);
      videoRef.current.volume = volume;
    }
  }, [volume]);

  const handleTimeUpdate = useCallback(() => {
    if (videoRef.current && !isDragging) {
      setCurrentTime(videoRef.current.currentTime);
    }
  }, [isDragging]);

  // Timeline click handler
  const handleTimelineClick = useCallback((e) => {
    if (timelineRef.current && videoRef.current && duration > 0) {
      const rect = timelineRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const percentage = clickX / rect.width;
      const newTime = percentage * duration;

      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  }, [duration]);

  // Volume control
  const handleVolumeChange = useCallback((newVolume) => {
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
  }, []);

  // Set cutting points
  const setStartPoint = useCallback(() => {
    setStartTime(currentTime);
  }, [currentTime]);

  const setEndPoint = useCallback(() => {
    setEndTime(currentTime);
  }, [currentTime]);

  // Export video segment
  const exportVideoSegment = useCallback(async () => {
    if (!videoFile || startTime >= endTime) {
      alert('يرجى تحديد نقاط البداية والنهاية بشكل صحيح');
      return;
    }

    setIsProcessing(true);

    try {
      // Create a simple video segment using canvas and MediaRecorder
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const video = videoRef.current;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      const stream = canvas.captureStream(30);
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm;codecs=vp9'
      });

      const chunks = [];
      mediaRecorder.ondataavailable = (e) => chunks.push(e.data);

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bandicut_segment_${Date.now()}.webm`;
        a.click();
        URL.revokeObjectURL(url);
        setIsProcessing(false);
      };

      // Start recording
      mediaRecorder.start();
      video.currentTime = startTime;

      const recordFrame = () => {
        if (video.currentTime >= endTime) {
          mediaRecorder.stop();
          return;
        }

        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        requestAnimationFrame(recordFrame);
      };

      video.play();
      recordFrame();

    } catch (error) {
      console.error('خطأ في تصدير الفيديو:', error);
      alert('حدث خطأ أثناء تصدير الفيديو');
      setIsProcessing(false);
    }
  }, [videoFile, startTime, endTime]);

  // Format time display
  const formatTime = useCallback((time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Calculate progress percentage
  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
  const startPercentage = duration > 0 ? (startTime / duration) * 100 : 0;
  const endPercentage = duration > 0 ? (endTime / duration) * 100 : 100;

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="logo">
          <div className="logo-icon">🎬</div>
          <h1 className="logo-text">BANDICUT</h1>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-content">
        {!videoFile ? (
          /* File Upload Area */
          <div 
            className="upload-area"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="upload-icon" size={48} />
            <div className="upload-text">اسحب ملف الفيديو هنا أو انقر للاختيار</div>
            <div className="upload-subtext">يدعم جميع صيغ الفيديو الشائعة</div>
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              onChange={handleFileInputChange}
              style={{ display: 'none' }}
            />
          </div>
        ) : (
          /* Video Player */
          <div className="video-container">
            <div className="video-player">
              {videoUrl ? (
                <video
                  ref={videoRef}
                  src={videoUrl}
                  onLoadedMetadata={handleVideoLoadedMetadata}
                  onTimeUpdate={handleTimeUpdate}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  controls={false}
                />
              ) : (
                <div className="video-placeholder">
                  <FileVideo size={64} />
                  <p>لم يتم تحميل فيديو بعد</p>
                </div>
              )}
            </div>
          </div>
        )}
      </main>

      {/* Controls */}
      {videoFile && (
        <div className="controls">
          <div className="control-group">
            <button className="btn" onClick={togglePlay}>
              {isPlaying ? <Pause size={16} /> : <Play size={16} />}
              {isPlaying ? 'إيقاف' : 'تشغيل'}
            </button>
            <button className="btn btn-secondary" onClick={() => videoRef.current && (videoRef.current.currentTime = 0)}>
              <SkipBack size={16} />
              البداية
            </button>
          </div>

          <div className="control-group">
            <button className="btn btn-secondary" onClick={setStartPoint} disabled={!videoFile}>
              <Scissors size={16} />
              نقطة البداية
            </button>
            <button className="btn btn-secondary" onClick={setEndPoint} disabled={!videoFile}>
              <Scissors size={16} />
              نقطة النهاية
            </button>
            <button
              className="btn"
              onClick={exportVideoSegment}
              disabled={!videoFile || startTime >= endTime || isProcessing}
            >
              {isProcessing ? <Loader size={16} className="spinning" /> : <Download size={16} />}
              {isProcessing ? 'جاري التصدير...' : 'تصدير المقطع'}
            </button>
          </div>

          <div className="control-group">
            <div className="volume-control">
              <Volume2 size={16} />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="volume-slider"
              />
            </div>
          </div>
        </div>
      )}

      {/* Timeline */}
      {videoFile && (
        <div className="timeline">
          <div
            className="timeline-track"
            ref={timelineRef}
            onClick={handleTimelineClick}
          >
            {/* Selection area */}
            <div
              className="timeline-selection"
              style={{
                left: `${startPercentage}%`,
                width: `${endPercentage - startPercentage}%`
              }}
            />
            {/* Current progress */}
            <div
              className="timeline-progress"
              style={{ width: `${progressPercentage}%` }}
            />
            {/* Start marker */}
            <div
              className="timeline-marker start-marker"
              style={{ left: `${startPercentage}%` }}
            />
            {/* End marker */}
            <div
              className="timeline-marker end-marker"
              style={{ left: `${endPercentage}%` }}
            />
            {/* Current time handle */}
            <div
              className="timeline-handle"
              style={{ left: `${progressPercentage}%` }}
            />
          </div>
          <div className="time-display">
            <span>البداية: {formatTime(startTime)}</span>
            <span>{formatTime(currentTime)} / {formatTime(duration)}</span>
            <span>النهاية: {formatTime(endTime)}</span>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
