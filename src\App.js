import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Upload,
  Play,
  Pause,
  Square,
  Scissors,
  Download,
  SkipBack,
  SkipForward,
  Volume2,
  FileVideo,
  Loader
} from 'lucide-react';

function App() {
  const [videoFile, setVideoFile] = useState(null);
  const [videoUrl, setVideoUrl] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [startTime, setStartTime] = useState(0);
  const [endTime, setEndTime] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [volume, setVolume] = useState(1);
  const [dragType, setDragType] = useState(null); // 'start', 'end', 'current'
  const [showPreview, setShowPreview] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const videoRef = useRef(null);
  const fileInputRef = useRef(null);
  const timelineRef = useRef(null);

  // Handle file upload
  const handleFileUpload = useCallback((file) => {
    if (file && file.type.startsWith('video/')) {
      setVideoFile(file);
      const url = URL.createObjectURL(file);
      setVideoUrl(url);
      setCurrentTime(0);
      setStartTime(0);
      setIsPlaying(false);
      setIsProcessing(false);
    } else {
      alert('يرجى اختيار ملف فيديو صالح');
    }
  }, []);

  // Handle drag and drop
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, [handleFileUpload]);

  // Handle file input change
  const handleFileInputChange = useCallback((e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileUpload(file);
    }
  }, [handleFileUpload]);

  // Video controls
  const togglePlay = useCallback(() => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  }, [isPlaying]);

  const handleVideoLoadedMetadata = useCallback(() => {
    if (videoRef.current) {
      const videoDuration = videoRef.current.duration;
      setDuration(videoDuration);
      setEndTime(videoDuration);
      videoRef.current.volume = volume;
    }
  }, [volume]);

  const handleTimeUpdate = useCallback(() => {
    if (videoRef.current && !isDragging) {
      setCurrentTime(videoRef.current.currentTime);
    }
  }, [isDragging]);

  // Timeline click handler
  const handleTimelineClick = useCallback((e) => {
    if (timelineRef.current && videoRef.current && duration > 0 && !isDragging) {
      const rect = timelineRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(1, clickX / rect.width));
      const newTime = percentage * duration;

      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  }, [duration, isDragging]);

  // Drag handlers
  const handleMouseDown = useCallback((e, type) => {
    e.stopPropagation();
    setIsDragging(true);
    setDragType(type);
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (!isDragging || !timelineRef.current || !dragType || duration === 0) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, mouseX / rect.width));
    const newTime = percentage * duration;

    if (dragType === 'start') {
      setStartTime(Math.min(newTime, endTime - 1));
    } else if (dragType === 'end') {
      setEndTime(Math.max(newTime, startTime + 1));
    } else if (dragType === 'current' && videoRef.current) {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  }, [isDragging, dragType, duration, startTime, endTime]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragType(null);
  }, []);

  // Add event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!videoFile) return;

      switch (e.key) {
        case ' ':
          e.preventDefault();
          togglePlay();
          break;
        case 'i':
        case 'I':
          setStartPoint();
          break;
        case 'o':
        case 'O':
          setEndPoint();
          break;
        case 'ArrowLeft':
          if (videoRef.current) {
            videoRef.current.currentTime = Math.max(0, currentTime - 5);
          }
          break;
        case 'ArrowRight':
          if (videoRef.current) {
            videoRef.current.currentTime = Math.min(duration, currentTime + 5);
          }
          break;
        case 'Home':
          jumpToTime(0);
          break;
        case 'End':
          jumpToTime(duration);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [videoFile, togglePlay, setStartPoint, setEndPoint, currentTime, duration, jumpToTime]);

  // Volume control
  const handleVolumeChange = useCallback((newVolume) => {
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
  }, []);

  // Set cutting points
  const setStartPoint = useCallback(() => {
    setStartTime(currentTime);
  }, [currentTime]);

  const setEndPoint = useCallback(() => {
    setEndTime(currentTime);
  }, [currentTime]);

  // Jump to specific time
  const jumpToTime = useCallback((time) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  }, []);

  // Preview functions
  const previewStart = useCallback(() => {
    jumpToTime(startTime);
    setShowPreview(true);
    setTimeout(() => setShowPreview(false), 2000);
  }, [startTime, jumpToTime]);

  const previewEnd = useCallback(() => {
    jumpToTime(Math.max(0, endTime - 3));
    setShowPreview(true);
    setTimeout(() => setShowPreview(false), 2000);
  }, [endTime, jumpToTime]);

  // Export video segment using a more efficient method
  const exportVideoSegment = useCallback(async () => {
    if (!videoFile || startTime >= endTime) {
      alert('يرجى تحديد نقاط البداية والنهاية بشكل صحيح');
      return;
    }

    setIsProcessing(true);

    try {
      // Method 1: Simple blob slicing for basic cutting (works for some formats)
      const segmentDuration = endTime - startTime;

      // Create a new video element for processing
      const tempVideo = document.createElement('video');
      tempVideo.src = videoUrl;
      tempVideo.muted = true;

      await new Promise((resolve) => {
        tempVideo.onloadedmetadata = resolve;
      });

      // Use MediaRecorder for re-encoding the segment
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      canvas.width = tempVideo.videoWidth || 640;
      canvas.height = tempVideo.videoHeight || 480;

      const stream = canvas.captureStream(30);
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm;codecs=vp8',
        videoBitsPerSecond: 2500000
      });

      const chunks = [];
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) chunks.push(e.data);
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bandicut_segment_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        setIsProcessing(false);
        alert('تم تصدير المقطع بنجاح!');
      };

      // Start the recording process
      tempVideo.currentTime = startTime;

      const startRecording = () => {
        mediaRecorder.start();
        tempVideo.play();

        const captureFrame = () => {
          if (tempVideo.currentTime >= endTime || tempVideo.ended) {
            mediaRecorder.stop();
            tempVideo.pause();
            setExportProgress(100);
            return;
          }

          // Update progress
          const progress = ((tempVideo.currentTime - startTime) / (endTime - startTime)) * 100;
          setExportProgress(Math.min(progress, 99));

          ctx.drawImage(tempVideo, 0, 0, canvas.width, canvas.height);
          requestAnimationFrame(captureFrame);
        };

        captureFrame();
      };

      tempVideo.onseeked = startRecording;

    } catch (error) {
      console.error('خطأ في تصدير الفيديو:', error);
      alert('حدث خطأ أثناء تصدير الفيديو. يرجى المحاولة مرة أخرى.');
      setIsProcessing(false);
    }
  }, [videoFile, startTime, endTime, videoUrl]);

  // Format time display
  const formatTime = useCallback((time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Calculate progress percentage
  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
  const startPercentage = duration > 0 ? (startTime / duration) * 100 : 0;
  const endPercentage = duration > 0 ? (endTime / duration) * 100 : 100;

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="logo">
          <div className="logo-icon">🎬</div>
          <h1 className="logo-text">BANDICUT</h1>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-content">
        {!videoFile ? (
          /* File Upload Area */
          <div 
            className="upload-area"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="upload-icon" size={48} />
            <div className="upload-text">اسحب ملف الفيديو هنا أو انقر للاختيار</div>
            <div className="upload-subtext">يدعم جميع صيغ الفيديو الشائعة</div>
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              onChange={handleFileInputChange}
              style={{ display: 'none' }}
            />
          </div>
        ) : (
          /* Video Player */
          <div className="video-container">
            <div className="video-player">
              {videoUrl ? (
                <video
                  ref={videoRef}
                  src={videoUrl}
                  onLoadedMetadata={handleVideoLoadedMetadata}
                  onTimeUpdate={handleTimeUpdate}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  controls={false}
                />
              ) : (
                <div className="video-placeholder">
                  <FileVideo size={64} />
                  <p>لم يتم تحميل فيديو بعد</p>
                </div>
              )}
            </div>
          </div>
        )}
      </main>

      {/* Controls */}
      {videoFile && (
        <div className="controls">
          <div className="control-group">
            <button className="btn" onClick={togglePlay}>
              {isPlaying ? <Pause size={16} /> : <Play size={16} />}
              {isPlaying ? 'إيقاف' : 'تشغيل'}
            </button>
            <button className="btn btn-secondary" onClick={() => videoRef.current && (videoRef.current.currentTime = 0)}>
              <SkipBack size={16} />
              البداية
            </button>
          </div>

          <div className="control-group">
            <button className="btn btn-secondary" onClick={setStartPoint} disabled={!videoFile}>
              <Scissors size={16} />
              نقطة البداية
            </button>
            <button className="btn btn-secondary" onClick={setEndPoint} disabled={!videoFile}>
              <Scissors size={16} />
              نقطة النهاية
            </button>
            <button className="btn btn-secondary" onClick={previewStart} disabled={!videoFile}>
              <Play size={16} />
              معاينة البداية
            </button>
            <button className="btn btn-secondary" onClick={previewEnd} disabled={!videoFile}>
              <Play size={16} />
              معاينة النهاية
            </button>
            <button
              className="btn"
              onClick={exportVideoSegment}
              disabled={!videoFile || startTime >= endTime || isProcessing}
            >
              {isProcessing ? <Loader size={16} className="spinning" /> : <Download size={16} />}
              {isProcessing ? `جاري التصدير... ${Math.round(exportProgress)}%` : 'تصدير المقطع'}
            </button>
          </div>

          {/* Export Progress */}
          {isProcessing && (
            <div className="export-progress">
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${exportProgress}%` }}
                />
              </div>
              <span className="progress-text">{Math.round(exportProgress)}%</span>
            </div>
          )}

          <div className="control-group">
            <div className="volume-control">
              <Volume2 size={16} />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="volume-slider"
              />
            </div>
          </div>
        </div>
      )}

      {/* Timeline */}
      {videoFile && (
        <div className="timeline">
          <div
            className="timeline-track"
            ref={timelineRef}
            onClick={handleTimelineClick}
          >
            {/* Selection area */}
            <div
              className="timeline-selection"
              style={{
                left: `${startPercentage}%`,
                width: `${endPercentage - startPercentage}%`
              }}
            />
            {/* Current progress */}
            <div
              className="timeline-progress"
              style={{ width: `${progressPercentage}%` }}
            />
            {/* Start marker */}
            <div
              className="timeline-marker start-marker"
              style={{ left: `${startPercentage}%` }}
              onMouseDown={(e) => handleMouseDown(e, 'start')}
              title={`نقطة البداية: ${formatTime(startTime)}`}
            />
            {/* End marker */}
            <div
              className="timeline-marker end-marker"
              style={{ left: `${endPercentage}%` }}
              onMouseDown={(e) => handleMouseDown(e, 'end')}
              title={`نقطة النهاية: ${formatTime(endTime)}`}
            />
            {/* Current time handle */}
            <div
              className="timeline-handle"
              style={{ left: `${progressPercentage}%` }}
              onMouseDown={(e) => handleMouseDown(e, 'current')}
              title={`الوقت الحالي: ${formatTime(currentTime)}`}
            />
          </div>
          <div className="time-display">
            <span>البداية: {formatTime(startTime)}</span>
            <span>{formatTime(currentTime)} / {formatTime(duration)}</span>
            <span>النهاية: {formatTime(endTime)}</span>
          </div>
          <div className="segment-info">
            <span>مدة المقطع: {formatTime(Math.max(0, endTime - startTime))}</span>
            <span>حجم الملف الأصلي: {videoFile ? (videoFile.size / (1024 * 1024)).toFixed(2) + ' MB' : '0 MB'}</span>
            {showPreview && <span className="preview-indicator">🔍 معاينة</span>}
          </div>

          {/* Keyboard Shortcuts Help */}
          <div className="shortcuts-help">
            <details>
              <summary>اختصارات لوحة المفاتيح</summary>
              <div className="shortcuts-list">
                <span><kbd>Space</kbd> تشغيل/إيقاف</span>
                <span><kbd>I</kbd> نقطة البداية</span>
                <span><kbd>O</kbd> نقطة النهاية</span>
                <span><kbd>←</kbd> تراجع 5 ثوان</span>
                <span><kbd>→</kbd> تقدم 5 ثوان</span>
                <span><kbd>Home</kbd> البداية</span>
                <span><kbd>End</kbd> النهاية</span>
              </div>
            </details>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
