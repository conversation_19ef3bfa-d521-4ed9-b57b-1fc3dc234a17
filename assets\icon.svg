<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="256" cy="256" r="240" fill="url(#gradient)" stroke="#1a252f" stroke-width="8"/>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="filmGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#34495e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2c3e50;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Film Strip -->
  <rect x="120" y="180" width="272" height="152" rx="12" ry="12" fill="url(#filmGradient)" stroke="#1a252f" stroke-width="3"/>
  
  <!-- Film Holes -->
  <circle cx="140" cy="200" r="8" fill="#1a252f"/>
  <circle cx="140" cy="230" r="8" fill="#1a252f"/>
  <circle cx="140" cy="260" r="8" fill="#1a252f"/>
  <circle cx="140" cy="290" r="8" fill="#1a252f"/>
  <circle cx="140" cy="320" r="8" fill="#1a252f"/>
  
  <circle cx="372" cy="200" r="8" fill="#1a252f"/>
  <circle cx="372" cy="230" r="8" fill="#1a252f"/>
  <circle cx="372" cy="260" r="8" fill="#1a252f"/>
  <circle cx="372" cy="290" r="8" fill="#1a252f"/>
  <circle cx="372" cy="320" r="8" fill="#1a252f"/>
  
  <!-- Film Frames -->
  <rect x="170" y="200" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="230" y="200" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="290" y="200" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  
  <rect x="170" y="280" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="230" y="280" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="290" y="280" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  
  <!-- Scissors Icon -->
  <g transform="translate(256, 256)">
    <!-- Scissors Blades -->
    <ellipse cx="-15" cy="-20" rx="8" ry="15" fill="#e74c3c" transform="rotate(-30)"/>
    <ellipse cx="15" cy="-20" rx="8" ry="15" fill="#e74c3c" transform="rotate(30)"/>
    
    <!-- Scissors Handles -->
    <circle cx="-12" cy="25" r="6" fill="#34495e"/>
    <circle cx="12" cy="25" r="6" fill="#34495e"/>
    
    <!-- Scissors Pivot -->
    <circle cx="0" cy="0" r="4" fill="#2c3e50"/>
    
    <!-- Cut Lines -->
    <line x1="-40" y1="-10" x2="-20" y2="-5" stroke="#f39c12" stroke-width="3" stroke-linecap="round"/>
    <line x1="20" y1="-5" x2="40" y2="-10" stroke="#f39c12" stroke-width="3" stroke-linecap="round"/>
  </g>
  
  <!-- Text -->
  <text x="256" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#2c3e50">BANDICUT</text>
  <text x="256" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="#7f8c8d">Video Editor</text>
</svg>
