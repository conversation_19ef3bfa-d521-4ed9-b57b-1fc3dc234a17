/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  height: 100vh;
  overflow: hidden;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: #27ae60;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #27ae60;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 20px;
}

/* File Upload Area */
.upload-area {
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: #27ae60;
}

.upload-area.dragover {
  background: rgba(39, 174, 96, 0.2);
  border-color: #27ae60;
}

.upload-icon {
  font-size: 48px;
  color: #27ae60;
  margin-bottom: 15px;
}

.upload-text {
  font-size: 18px;
  margin-bottom: 10px;
}

.upload-subtext {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

/* Video Player Area */
.video-container {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.video-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
}

video {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
  gap: 15px;
}

.video-placeholder p {
  font-size: 18px;
  margin: 0;
}

/* Controls */
.controls {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:hover {
  background: #219a52;
  transform: translateY(-1px);
}

.btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Volume Control */
.volume-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.volume-slider {
  width: 100px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #27ae60;
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #27ae60;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Spinning animation */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Timeline */
.timeline {
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.timeline-track {
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  position: relative;
  margin-bottom: 15px;
  cursor: pointer;
  overflow: hidden;
}

.timeline-selection {
  position: absolute;
  top: 0;
  height: 100%;
  background: rgba(39, 174, 96, 0.3);
  border: 2px solid #27ae60;
  border-radius: 5px;
  z-index: 1;
}

.timeline-progress {
  position: absolute;
  top: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  z-index: 2;
}

.timeline-marker {
  position: absolute;
  top: 0;
  width: 3px;
  height: 100%;
  z-index: 3;
}

.start-marker {
  background: #e74c3c;
  border-radius: 3px 0 0 3px;
}

.end-marker {
  background: #e74c3c;
  border-radius: 0 3px 3px 0;
}

.timeline-handle {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 4;
  border: 2px solid #27ae60;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* Responsive */
@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .control-group {
    width: 100%;
    justify-content: center;
  }
  
  .main-content {
    padding: 10px;
  }
}
