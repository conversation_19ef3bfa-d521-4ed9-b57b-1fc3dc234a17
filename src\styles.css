/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(39, 174, 96, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(52, 152, 219, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 20px;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  animation: logoGlow 3s ease-in-out infinite alternate;
}

.logo-text {
  font-size: 28px;
  font-weight: bold;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 10px rgba(39, 174, 96, 0.3);
}

@keyframes logoGlow {
  0% { box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3); }
  100% { box-shadow: 0 6px 25px rgba(39, 174, 96, 0.5); }
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 20px;
}

/* File Upload Area */
.upload-area {
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: #27ae60;
}

.upload-area.dragover {
  background: rgba(39, 174, 96, 0.2);
  border-color: #27ae60;
}

.upload-icon {
  font-size: 48px;
  color: #27ae60;
  margin-bottom: 15px;
}

.upload-text {
  font-size: 18px;
  margin-bottom: 10px;
}

.upload-subtext {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

/* Video Player Area */
.video-container {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.video-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
}

video {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
  gap: 15px;
}

.video-placeholder p {
  font-size: 18px;
  margin: 0;
}

/* Controls */
.controls {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.btn {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.2);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  background: linear-gradient(135deg, #219a52, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(39, 174, 96, 0.2);
}

.btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn:disabled::before {
  display: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Volume Control */
.volume-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.volume-slider {
  width: 100px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #27ae60;
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #27ae60;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Spinning animation */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Timeline */
.timeline {
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.timeline-track {
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  position: relative;
  margin-bottom: 15px;
  cursor: pointer;
  overflow: hidden;
}

.timeline-selection {
  position: absolute;
  top: 0;
  height: 100%;
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.3), rgba(46, 204, 113, 0.3));
  border: 2px solid #27ae60;
  border-radius: 5px;
  z-index: 1;
  box-shadow: 0 0 20px rgba(39, 174, 96, 0.2);
  animation: selectionPulse 2s ease-in-out infinite alternate;
}

@keyframes selectionPulse {
  0% { box-shadow: 0 0 20px rgba(39, 174, 96, 0.2); }
  100% { box-shadow: 0 0 30px rgba(39, 174, 96, 0.4); }
}

.timeline-progress {
  position: absolute;
  top: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  z-index: 2;
}

.timeline-marker {
  position: absolute;
  top: 0;
  width: 3px;
  height: 100%;
  z-index: 3;
  cursor: ew-resize;
  transition: all 0.2s ease;
}

.timeline-marker:hover {
  width: 5px;
  box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

.start-marker {
  background: #e74c3c;
  border-radius: 3px 0 0 3px;
}

.start-marker::before {
  content: '▶';
  position: absolute;
  top: -25px;
  left: -8px;
  background: #e74c3c;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  white-space: nowrap;
}

.end-marker {
  background: #e74c3c;
  border-radius: 0 3px 3px 0;
}

.end-marker::before {
  content: '◀';
  position: absolute;
  top: -25px;
  right: -8px;
  background: #e74c3c;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  white-space: nowrap;
}

.timeline-handle {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 4;
  border: 2px solid #27ae60;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
}

.segment-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  padding: 10px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-indicator {
  color: #27ae60;
  font-weight: bold;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Export Progress */
.export-progress {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #27ae60, #2ecc71);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  font-weight: bold;
  color: #27ae60;
  min-width: 40px;
  text-align: right;
}

/* Keyboard Shortcuts Help */
.shortcuts-help {
  padding: 10px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.shortcuts-help details {
  color: rgba(255, 255, 255, 0.8);
}

.shortcuts-help summary {
  cursor: pointer;
  font-size: 12px;
  padding: 5px 0;
  user-select: none;
}

.shortcuts-help summary:hover {
  color: #27ae60;
}

.shortcuts-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 10px;
  font-size: 11px;
}

.shortcuts-list span {
  display: flex;
  align-items: center;
  gap: 8px;
}

kbd {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 10px;
  font-family: monospace;
  color: #27ae60;
}

/* Responsive */
@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .control-group {
    width: 100%;
    justify-content: center;
  }
  
  .main-content {
    padding: 10px;
  }
}
