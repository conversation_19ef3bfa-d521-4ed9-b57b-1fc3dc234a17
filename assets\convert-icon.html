<!DOCTYPE html>
<html>
<head>
    <title>Icon Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        button {
            background: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #219a52;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Bandicut Clone Icon Converter</h1>
        <p>This tool converts the SVG icon to PNG format for Electron app.</p>
        
        <div id="svgContainer"></div>
        
        <canvas id="canvas512" width="512" height="512"></canvas>
        <br>
        <button onclick="downloadIcon(512)">Download 512x512 PNG</button>
        
        <canvas id="canvas256" width="256" height="256"></canvas>
        <br>
        <button onclick="downloadIcon(256)">Download 256x256 PNG</button>
        
        <canvas id="canvas128" width="128" height="128"></canvas>
        <br>
        <button onclick="downloadIcon(128)">Download 128x128 PNG</button>
        
        <canvas id="canvas64" width="64" height="64"></canvas>
        <br>
        <button onclick="downloadIcon(64)">Download 64x64 PNG</button>
        
        <canvas id="canvas32" width="32" height="32"></canvas>
        <br>
        <button onclick="downloadIcon(32)">Download 32x32 PNG</button>
    </div>

    <script>
        const svgString = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <circle cx="256" cy="256" r="240" fill="url(#gradient)" stroke="#1a252f" stroke-width="8"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="filmGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#34495e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2c3e50;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect x="120" y="180" width="272" height="152" rx="12" ry="12" fill="url(#filmGradient)" stroke="#1a252f" stroke-width="3"/>
  <circle cx="140" cy="200" r="8" fill="#1a252f"/>
  <circle cx="140" cy="230" r="8" fill="#1a252f"/>
  <circle cx="140" cy="260" r="8" fill="#1a252f"/>
  <circle cx="140" cy="290" r="8" fill="#1a252f"/>
  <circle cx="140" cy="320" r="8" fill="#1a252f"/>
  <circle cx="372" cy="200" r="8" fill="#1a252f"/>
  <circle cx="372" cy="230" r="8" fill="#1a252f"/>
  <circle cx="372" cy="260" r="8" fill="#1a252f"/>
  <circle cx="372" cy="290" r="8" fill="#1a252f"/>
  <circle cx="372" cy="320" r="8" fill="#1a252f"/>
  <rect x="170" y="200" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="230" y="200" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="290" y="200" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="170" y="280" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="230" y="280" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <rect x="290" y="280" width="50" height="40" rx="4" ry="4" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <g transform="translate(256, 256)">
    <ellipse cx="-15" cy="-20" rx="8" ry="15" fill="#e74c3c" transform="rotate(-30)"/>
    <ellipse cx="15" cy="-20" rx="8" ry="15" fill="#e74c3c" transform="rotate(30)"/>
    <circle cx="-12" cy="25" r="6" fill="#34495e"/>
    <circle cx="12" cy="25" r="6" fill="#34495e"/>
    <circle cx="0" cy="0" r="4" fill="#2c3e50"/>
    <line x1="-40" y1="-10" x2="-20" y2="-5" stroke="#f39c12" stroke-width="3" stroke-linecap="round"/>
    <line x1="20" y1="-5" x2="40" y2="-10" stroke="#f39c12" stroke-width="3" stroke-linecap="round"/>
  </g>
  <text x="256" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#2c3e50">BANDICUT</text>
  <text x="256" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="#7f8c8d">Video Editor</text>
</svg>`;

        function loadSVG() {
            const svgContainer = document.getElementById('svgContainer');
            svgContainer.innerHTML = svgString;
            
            // Convert to different sizes
            [512, 256, 128, 64, 32].forEach(size => {
                convertSVGToCanvas(size);
            });
        }

        function convertSVGToCanvas(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            const svgBlob = new Blob([svgString], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.clearRect(0, 0, size, size);
                ctx.drawImage(img, 0, 0, size, size);
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        }

        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        // Load SVG when page loads
        window.onload = loadSVG;
    </script>
</body>
</html>
