# Bandicut Clone - قاطع الفيديو

تطبيق ويب مشابه لبرنامج Bandicut لقطع وتحرير الفيديو، مبني باستخدام React.

## المميزات

### ✨ الوظائف الأساسية
- 📁 رفع ملفات الفيديو بالسحب والإفلات
- ▶️ تشغيل وإيقاف الفيديو
- ✂️ تحديد نقاط البداية والنهاية للقطع
- 📥 تصدير المقطع المحدد
- 🔊 التحكم في مستوى الصوت

### 🎛️ أدوات التحكم المتقدمة
- 🖱️ خط زمني تفاعلي قابل للسحب
- 🎯 علامات قابلة للسحب لتحديد نقاط القطع
- 👀 معاينة نقاط البداية والنهاية
- ⌨️ اختصارات لوحة المفاتيح
- 📊 شريط تقدم التصدير

### 🎨 التصميم
- 🌙 تصميم داكن أنيق
- 🎭 تأثيرات بصرية متقدمة
- 📱 تصميم متجاوب
- 🌍 دعم اللغة العربية

## اختصارات لوحة المفاتيح

| المفتاح | الوظيفة |
|---------|---------|
| `Space` | تشغيل/إيقاف |
| `I` | تحديد نقطة البداية |
| `O` | تحديد نقطة النهاية |
| `←` | تراجع 5 ثوان |
| `→` | تقدم 5 ثوان |
| `Home` | الانتقال للبداية |
| `End` | الانتقال للنهاية |

## التقنيات المستخدمة

- **React** - مكتبة واجهة المستخدم
- **Webpack** - أداة البناء والتطوير
- **Lucide React** - مكتبة الأيقونات
- **CSS3** - التصميم والتأثيرات
- **MediaRecorder API** - تسجيل وتصدير الفيديو
- **Canvas API** - معالجة الفيديو

## التشغيل

### المتطلبات
- Node.js (الإصدار 14 أو أحدث)
- npm أو yarn

### التثبيت والتشغيل

```bash
# تثبيت التبعيات
npm install

# تشغيل الخادم المحلي للتطوير
npm run dev

# بناء المشروع للإنتاج
npm run build
```

سيتم تشغيل التطبيق على `http://localhost:3000`

## كيفية الاستخدام

1. **رفع الفيديو**: اسحب ملف الفيديو إلى المنطقة المخصصة أو انقر لاختيار الملف
2. **تشغيل الفيديو**: استخدم أزرار التحكم أو مفتاح المسافة
3. **تحديد المقطع**: 
   - انقر على "نقطة البداية" أو اضغط `I` لتحديد بداية المقطع
   - انقر على "نقطة النهاية" أو اضغط `O` لتحديد نهاية المقطع
   - أو اسحب العلامات على الخط الزمني
4. **معاينة**: استخدم أزرار "معاينة البداية" و "معاينة النهاية"
5. **التصدير**: انقر على "تصدير المقطع" لحفظ الجزء المحدد

## الملفات الرئيسية

```
src/
├── App.js          # المكون الرئيسي
├── index.js        # نقطة دخول التطبيق
└── styles.css      # ملف التصميم الرئيسي

public/
└── index.html      # صفحة HTML الأساسية

package.json        # تبعيات المشروع
webpack.config.js   # إعدادات Webpack
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة (`git checkout -b feature/AmazingFeature`)
3. تنفيذ التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. رفع التغييرات (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح [Issue](../../issues) جديد.

---

**ملاحظة**: هذا التطبيق مخصص للأغراض التعليمية والتطويرية. للاستخدام التجاري، يرجى التأكد من توافق التراخيص.
